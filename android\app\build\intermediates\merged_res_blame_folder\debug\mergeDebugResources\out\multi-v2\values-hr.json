{"logs": [{"outputFile": "com.alr.spendwise.app-mergeDebugResources-2:/values-hr/values-hr.xml", "map": [{"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\eb1abcd433ebd6830d96edea1f30a050\\transformed\\biometric-1.1.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,381,513,641,770,901,1035,1135,1269,1402", "endColumns": "110,94,119,131,127,128,130,133,99,133,132,122", "endOffsets": "161,256,376,508,636,765,896,1030,1130,1264,1397,1520"}, "to": {"startLines": "79,81,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8534,8750,9161,9281,9413,9541,9670,9801,9935,10035,10169,10302", "endColumns": "110,94,119,131,127,128,130,133,99,133,132,122", "endOffsets": "8640,8840,9276,9408,9536,9665,9796,9930,10030,10164,10297,10420"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "31,32,33,34,35,36,37,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3049,3147,3254,3351,3450,3554,3658,10510", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3142,3249,3346,3445,3549,3653,3770,10606"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\312f1400fd07d3c14522fb116debe31d\\transformed\\credentials-1.3.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2816,2927", "endColumns": "110,121", "endOffsets": "2922,3044"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\697a983ff8b6be23efe7df3e3bbc5a94\\transformed\\play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "69", "startColumns": "4", "startOffsets": "7359", "endColumns": "131", "endOffsets": "7486"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\538a1cd0f10e1c172df76f4159db47aa\\transformed\\browser-1.4.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "80,82,83,84", "startColumns": "4,4,4,4", "startOffsets": "8645,8845,8945,9059", "endColumns": "104,99,113,101", "endOffsets": "8745,8940,9054,9156"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,10425", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,10505"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\1e2a4bdd6b7dd0849fedecef3b361d31\\transformed\\facebook-login-18.0.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,356,497,612,716,808,886,977,1067,1179,1293,1383,1473,1580,1705,1788,1873,2058,2153,2257,2371,2476", "endColumns": "164,135,140,114,103,91,77,90,89,111,113,89,89,106,124,82,84,184,94,103,113,104,150", "endOffsets": "215,351,492,607,711,803,881,972,1062,1174,1288,1378,1468,1575,1700,1783,1868,2053,2148,2252,2366,2471,2622"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3775,3940,4076,4217,4332,4436,4528,4606,4697,4787,4899,5013,5103,5193,5300,5425,5508,5593,5778,5873,5977,6091,6196", "endColumns": "164,135,140,114,103,91,77,90,89,111,113,89,89,106,124,82,84,184,94,103,113,104,150", "endOffsets": "3935,4071,4212,4327,4431,4523,4601,4692,4782,4894,5008,5098,5188,5295,5420,5503,5588,5773,5868,5972,6086,6191,6342"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\3897ee7a3a7e64eb47ff9b7bb8256b24\\transformed\\play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "61,62,63,64,65,66,67,68,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6347,6453,6610,6740,6850,7007,7137,7252,7491,7641,7748,7905,8033,8180,8323,8391,8453", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "6448,6605,6735,6845,7002,7132,7247,7354,7636,7743,7900,8028,8175,8318,8386,8448,8529"}}]}]}