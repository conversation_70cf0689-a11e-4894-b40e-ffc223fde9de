{"dock": {"expenses": "Expenses", "statistics": "Statistics", "notes": "Notes", "settings": "Settings"}, "expenses": {"table": {"header": {"date": "Date", "expense": "Expense", "amount": "Amount"}}, "modal": {"create": {"form": {"name": "Expense Name", "date": "Date", "amount": "Amount", "type": {"title": "Type", "expense": "Expense", "income": "Income"}, "note": "Notes", "recurring": "Recurring"}, "title": "Create expenses"}, "edit": {"title": "Edit expenses"}}, "filter": {"search": "Enter keyword..."}}, "calendar": {"clear": "Delete"}, "settings": {"account": {"google": {"logout": {"title": "Logout", "desc": "Logout to your google account", "success": "Logout google successfully"}, "login": {"title": "<PERSON><PERSON>", "desc": "Login to your google account", "success": "Logged google successfully"}}}, "theme": {"title": "Theme", "desc": "Choose the theme of the app"}, "language": {"title": "Language", "desc": "Choose the language of the app"}, "notification": {"title": "Notification", "desc": "Turn On/Off the notification of the app"}, "data": {"import": {"title": "Import", "desc": "Import the data expenses", "error": "An error occurred when importing data", "success": "Import data successfully"}, "export": {"title": "Export", "desc": "Export the data expenses", "error": "An error occurred when exporting data", "success": "Export data successfully"}, "cache": {"title": "Delete Cache", "desc": "Delete cache data of the app"}, "title": "Data", "desc": "Set import, export and synchronize data", "backup": {"title": "Backup", "description": "Click to backup manual data"}, "sync": {"title": "Sync", "description": "Click to sync manual data"}}, "autoUpdate": {"title": "Auto Update", "desc": "Turn On/Off auto update of the app"}, "checkUpdate": {"title": "Check Update"}, "general": {"checkUpdate": {"desc": "Manual check new version of the app"}}, "defaultPage": {"title": "<PERSON><PERSON><PERSON>", "desc": "Choose the page when open the app"}}, "btn": {"cancel": "Cancel", "save": "Save"}, "toast": {"title": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Info"}}}