{"logs": [{"outputFile": "com.alr.spendwise.app-mergeDebugResources-2:/values-uz/values-uz.xml", "map": [{"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\312f1400fd07d3c14522fb116debe31d\\transformed\\credentials-1.3.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,109", "endOffsets": "159,269"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2776,2885", "endColumns": "108,109", "endOffsets": "2880,2990"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "31,32,33,34,35,36,37,73", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2995,3097,3199,3300,3400,3508,3612,8023", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3092,3194,3295,3395,3503,3607,3726,8119"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\3897ee7a3a7e64eb47ff9b7bb8256b24\\transformed\\play-services-base-18.5.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3731,3836,3986,4115,4224,4369,4502,4622,4878,5050,5158,5317,5449,5603,5765,5831,5892", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "3831,3981,4110,4219,4364,4497,4617,4723,5045,5153,5312,5444,5598,5760,5826,5887,5967"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,7939", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,8018"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\697a983ff8b6be23efe7df3e3bbc5a94\\transformed\\play-services-basement-18.4.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4728", "endColumns": "149", "endOffsets": "4873"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\538a1cd0f10e1c172df76f4159db47aa\\transformed\\browser-1.4.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6086,6294,6403,6513", "endColumns": "117,108,109,105", "endOffsets": "6199,6398,6508,6614"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\eb1abcd433ebd6830d96edea1f30a050\\transformed\\biometric-1.1.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,259,379,510,638,785,917,1062,1159,1298,1438", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "164,254,374,505,633,780,912,1057,1154,1293,1433,1574"}, "to": {"startLines": "56,58,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5972,6204,6619,6739,6870,6998,7145,7277,7422,7519,7658,7798", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "6081,6289,6734,6865,6993,7140,7272,7417,7514,7653,7793,7934"}}]}]}