{"logs": [{"outputFile": "com.alr.spendwise.app-mergeDebugResources-2:/values-gu/values-gu.xml", "map": [{"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\697a983ff8b6be23efe7df3e3bbc5a94\\transformed\\play-services-basement-18.4.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "69", "startColumns": "4", "startOffsets": "7255", "endColumns": "146", "endOffsets": "7397"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\312f1400fd07d3c14522fb116debe31d\\transformed\\credentials-1.3.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2788,2899", "endColumns": "110,111", "endOffsets": "2894,3006"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\eb1abcd433ebd6830d96edea1f30a050\\transformed\\biometric-1.1.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,254,379,506,637,780,915,1057,1154,1291,1429", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "163,249,374,501,632,775,910,1052,1149,1286,1424,1544"}, "to": {"startLines": "79,81,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8449,8663,9060,9185,9312,9443,9586,9721,9863,9960,10097,10235", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "8557,8744,9180,9307,9438,9581,9716,9858,9955,10092,10230,10350"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\538a1cd0f10e1c172df76f4159db47aa\\transformed\\browser-1.4.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "80,82,83,84", "startColumns": "4,4,4,4", "startOffsets": "8562,8749,8850,8959", "endColumns": "100,100,108,100", "endOffsets": "8658,8845,8954,9055"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\1e2a4bdd6b7dd0849fedecef3b361d31\\transformed\\facebook-login-18.0.2\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,209,341,480,590,678,767,845,934,1027,1142,1253,1348,1443,1548,1678,1759,1843,2021,2117,2218,2336,2446", "endColumns": "153,131,138,109,87,88,77,88,92,114,110,94,94,104,129,80,83,177,95,100,117,109,152", "endOffsets": "204,336,475,585,673,762,840,929,1022,1137,1248,1343,1438,1543,1673,1754,1838,2016,2112,2213,2331,2441,2594"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3729,3883,4015,4154,4264,4352,4441,4519,4608,4701,4816,4927,5022,5117,5222,5352,5433,5517,5695,5791,5892,6010,6120", "endColumns": "153,131,138,109,87,88,77,88,92,114,110,94,94,104,129,80,83,177,95,100,117,109,152", "endOffsets": "3878,4010,4149,4259,4347,4436,4514,4603,4696,4811,4922,5017,5112,5217,5347,5428,5512,5690,5786,5887,6005,6115,6268"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\3897ee7a3a7e64eb47ff9b7bb8256b24\\transformed\\play-services-base-18.5.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "61,62,63,64,65,66,67,68,70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6273,6381,6547,6672,6784,6922,7044,7155,7402,7550,7658,7822,7947,8090,8240,8301,8367", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "6376,6542,6667,6779,6917,7039,7150,7250,7545,7653,7817,7942,8085,8235,8296,8362,8444"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,10355", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,10431"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "31,32,33,34,35,36,37,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3011,3105,3208,3305,3407,3509,3607,10436", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3100,3203,3300,3402,3504,3602,3724,10532"}}]}]}