import { CapacitorHttp } from '@capacitor/core';
import { Network } from '@capacitor/network';
import { Preferences } from '@capacitor/preferences';
import { SocialLogin } from '@capgo/capacitor-social-login';
import { SecureStoragePlugin } from 'capacitor-secure-storage-plugin';

class GoogleService {
  async initialize() {
    console.log('Initialize Google Service');
    await SocialLogin.initialize({
      google: {
        mode: 'offline',
        webClientId: import.meta.env.VITE_GOOGLE_CLIENT_ID,
      },
    });
  }

  async login() {
    try {
      const loginResult = await SocialLogin.login({
        provider: 'google',
        options: {
          forceRefreshToken: true,
          scopes: ['https://www.googleapis.com/auth/drive.appfolder', 'https://www.googleapis.com/auth/drive.file'],
        },
      });

      const authCode = (loginResult as any).result.serverAuthCode;

      if (!authCode) throw Error('No auth code');

      // Check internet
      const currentNetwork = await Network.getStatus();
      if (!currentNetwork.connected) throw Error('No internet connection');

      // Get token
      const data = new URLSearchParams();
      data.append('client_id', import.meta.env.VITE_GOOGLE_CLIENT_ID);
      data.append('client_secret', import.meta.env.VITE_GOOGLE_CLIENT_SECRET);
      data.append('code', authCode);
      data.append('grant_type', 'authorization_code');
      data.append('redirect_uri', import.meta.env.VITE_GOOGLE_REDIRECT_URI);

      const tokenResult = await CapacitorHttp.post({
        url: 'https://oauth2.googleapis.com/token',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: data.toString(),
      });

      if (tokenResult.status != 200) throw Error('Failed to get token');

      const { access_token, expires_in, refresh_token, id_token } = tokenResult.data;

      const expiresAt = (Date.now() + Number(expires_in) * 1000).toString();

      await Promise.all([
        SecureStoragePlugin.set({ key: 'access_token', value: access_token }),
        SecureStoragePlugin.set({ key: 'refresh_token', value: refresh_token }),
        SecureStoragePlugin.set({ key: 'id_token', value: id_token }),
        Preferences.set({ key: 'expires_at', value: expiresAt }),
      ]);

      return {
        success: true,
        message: 'Login successfully',
        data: { access_token, expires_in, refresh_token, id_token },
      };
    } catch (e: any) {
      console.log('Error', e);
      return {
        success: false,
        message: e.message,
        data: null,
      };
    }
  }

  async logout() {
    await Promise.all([
      SecureStoragePlugin.remove({ key: 'access_token' }),
      SecureStoragePlugin.remove({ key: 'refresh_token' }),
      SecureStoragePlugin.remove({ key: 'id_token' }),
      Preferences.remove({ key: 'expires_at' }),
    ]);

    return {
      success: true,
      message: 'Logout successfully',
    };
  }

  async isLoggedIn() {
    try {
      const refresh_token = await SecureStoragePlugin.get({ key: 'refresh_token' });
      return { success: !!refresh_token.value, message: 'Check login status successfully' };
    } catch (e) {
      return { success: false, message: 'Check login status failed' };
    }
  }
}

export const googleService = new GoogleService();
