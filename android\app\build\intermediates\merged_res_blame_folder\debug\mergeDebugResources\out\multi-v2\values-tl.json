{"logs": [{"outputFile": "com.alr.spendwise.app-mergeDebugResources-2:/values-tl/values-tl.xml", "map": [{"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\1e2a4bdd6b7dd0849fedecef3b361d31\\transformed\\facebook-login-18.0.2\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,373,536,623,710,790,881,974,1095,1205,1300,1395,1501,1625,1708,1792,1985,2078,2180,2298,2412", "endColumns": "173,143,162,86,86,79,90,92,120,109,94,94,105,123,82,83,192,92,101,117,113,156", "endOffsets": "224,368,531,618,705,785,876,969,1090,1200,1295,1390,1496,1620,1703,1787,1980,2073,2175,2293,2407,2564"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3805,3979,4123,4286,4373,4460,4540,4631,4724,4845,4955,5050,5145,5251,5375,5458,5542,5735,5828,5930,6048,6162", "endColumns": "173,143,162,86,86,79,90,92,120,109,94,94,105,123,82,83,192,92,101,117,113,156", "endOffsets": "3974,4118,4281,4368,4455,4535,4626,4719,4840,4950,5045,5140,5246,5370,5453,5537,5730,5823,5925,6043,6157,6314"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,10665", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,10745"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\538a1cd0f10e1c172df76f4159db47aa\\transformed\\browser-1.4.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "79,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "8795,8994,9099,9210", "endColumns": "102,104,110,104", "endOffsets": "8893,9094,9205,9310"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\312f1400fd07d3c14522fb116debe31d\\transformed\\credentials-1.3.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2845,2957", "endColumns": "111,113", "endOffsets": "2952,3066"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\697a983ff8b6be23efe7df3e3bbc5a94\\transformed\\play-services-basement-18.4.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "68", "startColumns": "4", "startOffsets": "7378", "endColumns": "144", "endOffsets": "7518"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\eb1abcd433ebd6830d96edea1f30a050\\transformed\\biometric-1.1.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,265,387,540,683,835,962,1100,1200,1337,1489", "endColumns": "113,95,121,152,142,151,126,137,99,136,151,125", "endOffsets": "164,260,382,535,678,830,957,1095,1195,1332,1484,1610"}, "to": {"startLines": "78,80,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8681,8898,9315,9437,9590,9733,9885,10012,10150,10250,10387,10539", "endColumns": "113,95,121,152,142,151,126,137,99,136,151,125", "endOffsets": "8790,8989,9432,9585,9728,9880,10007,10145,10245,10382,10534,10660"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "31,32,33,34,35,36,37,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3071,3168,3270,3371,3468,3575,3683,10750", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3163,3265,3366,3463,3570,3678,3800,10846"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\3897ee7a3a7e64eb47ff9b7bb8256b24\\transformed\\play-services-base-18.5.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "60,61,62,63,64,65,66,67,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6319,6426,6602,6740,6849,7007,7143,7265,7523,7702,7809,7987,8125,8287,8466,8534,8600", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "6421,6597,6735,6844,7002,7138,7260,7373,7697,7804,7982,8120,8282,8461,8529,8595,8676"}}]}]}