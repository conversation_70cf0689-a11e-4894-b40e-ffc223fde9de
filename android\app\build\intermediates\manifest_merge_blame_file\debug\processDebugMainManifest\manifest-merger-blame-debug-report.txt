1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alr.spendwise"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:40:5-67
13-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:40:22-64
14    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
14-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:41:5-80
14-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:41:22-77
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:42:5-80
15-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:42:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:43:5-81
16-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:43:22-78
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->[:capacitor-haptics] D:\MyProjects\SpendWise-New\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
17-->[:capacitor-haptics] D:\MyProjects\SpendWise-New\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->[:capacitor-network] D:\MyProjects\SpendWise-New\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
18-->[:capacitor-network] D:\MyProjects\SpendWise-New\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
19    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
19-->[:capgo-capacitor-social-login] D:\MyProjects\SpendWise-New\node_modules\@capgo\capacitor-social-login\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-74
19-->[:capgo-capacitor-social-login] D:\MyProjects\SpendWise-New\node_modules\@capgo\capacitor-social-login\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-71
20    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
20-->[androidx.biometric:biometric:1.1.0] D:\Tools\.gradle\caches\8.11.1\transforms\eb1abcd433ebd6830d96edea1f30a050\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
20-->[androidx.biometric:biometric:1.1.0] D:\Tools\.gradle\caches\8.11.1\transforms\eb1abcd433ebd6830d96edea1f30a050\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
21    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
21-->[androidx.biometric:biometric:1.1.0] D:\Tools\.gradle\caches\8.11.1\transforms\eb1abcd433ebd6830d96edea1f30a050\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
21-->[androidx.biometric:biometric:1.1.0] D:\Tools\.gradle\caches\8.11.1\transforms\eb1abcd433ebd6830d96edea1f30a050\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
22
23    <queries>
23-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:15:5-17:15
24        <package android:name="com.facebook.katana" />
24-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:16:9-55
24-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:16:18-52
25
26        <intent>
26-->[com.google.androidbrowserhelper:androidbrowserhelper:2.5.0] D:\Tools\.gradle\caches\8.11.1\transforms\824fcae5335998d0d778aa7eadecdfb2\transformed\androidbrowserhelper-2.5.0\AndroidManifest.xml:27:9-33:18
27            <action android:name="android.intent.action.VIEW" />
27-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:30:17-69
27-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:30:25-66
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:33:17-78
29-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:33:27-75
30
31            <data android:scheme="https" />
31-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:35:17-37:50
31-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:37:21-47
32        </intent>
33    </queries>
34
35    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
35-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:14:5-79
35-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:14:22-76
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
36-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:16:5-88
36-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:16:22-85
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
37-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:17:5-82
37-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:17:22-79
38    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
38-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:18:5-92
38-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:18:22-89
39
40    <permission
40-->[androidx.core:core:1.15.0] D:\Tools\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
41        android:name="com.alr.spendwise.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.15.0] D:\Tools\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.15.0] D:\Tools\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.alr.spendwise.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.15.0] D:\Tools\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.15.0] D:\Tools\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
45    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
45-->[com.android.installreferrer:installreferrer:1.0] D:\Tools\.gradle\caches\8.11.1\transforms\22b5f4a9ea7bac3252130299fb86cf18\transformed\installreferrer-1.0\AndroidManifest.xml:9:5-110
45-->[com.android.installreferrer:installreferrer:1.0] D:\Tools\.gradle\caches\8.11.1\transforms\22b5f4a9ea7bac3252130299fb86cf18\transformed\installreferrer-1.0\AndroidManifest.xml:9:22-107
46
47    <application
47-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:4:5-36:19
48        android:allowBackup="true"
48-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:5:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.15.0] D:\Tools\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="false"
52        android:icon="@mipmap/ic_launcher"
52-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:6:9-43
53        android:label="@string/app_name"
53-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:7:9-41
54        android:roundIcon="@mipmap/ic_launcher_round"
54-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:8:9-54
55        android:supportsRtl="true"
55-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:9:9-35
56        android:theme="@style/AppTheme" >
56-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:10:9-40
57        <activity
57-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:12:9-25:20
58            android:name="com.alr.spendwise.MainActivity"
58-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:14:13-41
59            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
59-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:13:13-140
60            android:exported="true"
60-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:18:13-36
61            android:label="@string/title_activity_main"
61-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:15:13-56
62            android:launchMode="singleTask"
62-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:17:13-44
63            android:theme="@style/AppTheme.NoActionBarLaunch" >
63-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:16:13-62
64            <intent-filter>
64-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:20:13-23:29
65                <action android:name="android.intent.action.MAIN" />
65-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:21:17-69
65-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:21:25-66
66
67                <category android:name="android.intent.category.LAUNCHER" />
67-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:22:17-77
67-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:22:27-74
68            </intent-filter>
69        </activity>
70
71        <provider
72            android:name="androidx.core.content.FileProvider"
72-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:28:13-62
73            android:authorities="com.alr.spendwise.fileprovider"
73-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:29:13-64
74            android:exported="false"
74-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:30:13-37
75            android:grantUriPermissions="true" >
75-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:31:13-47
76            <meta-data
76-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:32:13-34:64
77                android:name="android.support.FILE_PROVIDER_PATHS"
77-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:33:17-67
78                android:resource="@xml/file_paths" />
78-->D:\MyProjects\SpendWise-New\android\app\src\main\AndroidManifest.xml:34:17-51
79        </provider>
80
81        <activity
81-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:20:9-23:66
82            android:name="com.facebook.FacebookActivity"
82-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:21:13-57
83            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
83-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:22:13-96
84            android:theme="@style/com_facebook_activity_theme" />
84-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:23:13-63
85        <activity android:name="com.facebook.CustomTabMainActivity" />
85-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:24:9-71
85-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:24:19-68
86        <activity
86-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:25:9-39:20
87            android:name="com.facebook.CustomTabActivity"
87-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:26:13-58
88            android:exported="true" >
88-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:27:13-36
89            <intent-filter>
89-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:29:13-38:29
90                <action android:name="android.intent.action.VIEW" />
90-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:30:17-69
90-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:30:25-66
91
92                <category android:name="android.intent.category.DEFAULT" />
92-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:32:17-76
92-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:32:27-73
93                <category android:name="android.intent.category.BROWSABLE" />
93-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:33:17-78
93-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:33:27-75
94
95                <data
95-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:35:17-37:50
96                    android:host="cct.com.alr.spendwise"
96-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:36:21-56
97                    android:scheme="fbconnect" />
97-->[com.facebook.android:facebook-common:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\0dd0d3e8de446dcc2045bd19e68aeec6\transformed\facebook-common-18.0.2\AndroidManifest.xml:37:21-47
98            </intent-filter>
99        </activity>
100
101        <service
101-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:24:9-32:19
102            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
102-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:25:13-94
103            android:enabled="true"
103-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:26:13-35
104            android:exported="false" >
104-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:27:13-37
105            <meta-data
105-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:29:13-31:104
106                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
106-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:30:17-76
107                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
107-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:31:17-101
108        </service>
109
110        <activity
110-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:34:9-41:20
111            android:name="androidx.credentials.playservices.HiddenActivity"
111-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:35:13-76
112            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
112-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:36:13-87
113            android:enabled="true"
113-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:37:13-35
114            android:exported="false"
114-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:38:13-37
115            android:fitsSystemWindows="true"
115-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:39:13-45
116            android:theme="@style/Theme.Hidden" >
116-->[androidx.credentials:credentials-play-services-auth:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\499bfd9d44ce23ed65691945ea1ce485\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:40:13-48
117        </activity>
118        <activity
118-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
119            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
119-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
120            android:excludeFromRecents="true"
120-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
121            android:exported="false"
121-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
122            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
122-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
123        <!--
124            Service handling Google Sign-In user revocation. For apps that do not integrate with
125            Google Sign-In, this service will never be started.
126        -->
127        <service
127-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
128            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
128-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
129            android:exported="true"
129-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
130            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
130-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
131            android:visibleToInstantApps="true" />
131-->[com.google.android.gms:play-services-auth:21.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\2f342ca0f0f69589f79d1a0af4f981e5\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
132
133        <activity
133-->[com.google.android.gms:play-services-base:18.5.0] D:\Tools\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
134            android:name="com.google.android.gms.common.api.GoogleApiActivity"
134-->[com.google.android.gms:play-services-base:18.5.0] D:\Tools\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
135            android:exported="false"
135-->[com.google.android.gms:play-services-base:18.5.0] D:\Tools\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
136            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
136-->[com.google.android.gms:play-services-base:18.5.0] D:\Tools\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
137
138        <meta-data
138-->[com.google.android.gms:play-services-basement:18.4.0] D:\Tools\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
139            android:name="com.google.android.gms.version"
139-->[com.google.android.gms:play-services-basement:18.4.0] D:\Tools\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
140            android:value="@integer/google_play_services_version" />
140-->[com.google.android.gms:play-services-basement:18.4.0] D:\Tools\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
141
142        <provider
142-->[androidx.emoji2:emoji2:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
143            android:name="androidx.startup.InitializationProvider"
143-->[androidx.emoji2:emoji2:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
144            android:authorities="com.alr.spendwise.androidx-startup"
144-->[androidx.emoji2:emoji2:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
145            android:exported="false" >
145-->[androidx.emoji2:emoji2:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
146            <meta-data
146-->[androidx.emoji2:emoji2:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
147                android:name="androidx.emoji2.text.EmojiCompatInitializer"
147-->[androidx.emoji2:emoji2:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
148                android:value="androidx.startup" />
148-->[androidx.emoji2:emoji2:1.3.0] D:\Tools\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
149            <meta-data
149-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Tools\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
150-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Tools\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
151                android:value="androidx.startup" />
151-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Tools\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
152            <meta-data
152-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
153                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
153-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
154                android:value="androidx.startup" />
154-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
155        </provider>
156
157        <receiver
157-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:36:9-42:20
158            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
158-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:37:13-86
159            android:exported="false" >
159-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:38:13-37
160            <intent-filter>
160-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:39:13-41:29
161                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
161-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:40:17-95
161-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:40:25-92
162            </intent-filter>
163        </receiver>
164        <receiver
164-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:43:9-49:20
165            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
165-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:44:13-118
166            android:exported="false" >
166-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:45:13-37
167            <intent-filter>
167-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:46:13-48:29
168                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
168-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:47:17-103
168-->[com.facebook.android:facebook-core:18.0.2] D:\Tools\.gradle\caches\8.11.1\transforms\8f220f5a2b7c856d4ca4ec161375c40e\transformed\facebook-core-18.0.2\AndroidManifest.xml:47:25-100
169            </intent-filter>
170        </receiver>
171        <receiver
171-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
172            android:name="androidx.profileinstaller.ProfileInstallReceiver"
172-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
173            android:directBootAware="false"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
174            android:enabled="true"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
175            android:exported="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
176            android:permission="android.permission.DUMP" >
176-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
178                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
178-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
181                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
181-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
182            </intent-filter>
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
184                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
184-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
187                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
187-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Tools\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
188            </intent-filter>
189        </receiver>
190
191        <service
191-->[androidx.room:room-runtime:2.6.1] D:\Tools\.gradle\caches\8.11.1\transforms\306dbe0c29ebe7db5878e236f08ceafe\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
192            android:name="androidx.room.MultiInstanceInvalidationService"
192-->[androidx.room:room-runtime:2.6.1] D:\Tools\.gradle\caches\8.11.1\transforms\306dbe0c29ebe7db5878e236f08ceafe\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
193            android:directBootAware="true"
193-->[androidx.room:room-runtime:2.6.1] D:\Tools\.gradle\caches\8.11.1\transforms\306dbe0c29ebe7db5878e236f08ceafe\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
194            android:exported="false" />
194-->[androidx.room:room-runtime:2.6.1] D:\Tools\.gradle\caches\8.11.1\transforms\306dbe0c29ebe7db5878e236f08ceafe\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
195    </application>
196
197</manifest>
