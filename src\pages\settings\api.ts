import dayjs from 'dayjs';

import { query } from '~/assets/libs/nosqlite';
import { expenseModel, noteModel } from '~/configs/model';
import { ExpenseModel, ExpenseType } from '~/models/expenseModel';
import { NoteModel } from '~/models/noteModel';

export async function handleImportData(data: any, deleteData?: boolean) {
  if (deleteData) {
    await query('DELETE FROM expense');
    await query('DELETE FROM note');
  }

  data = convertData(data);

  await expenseModel.insertMany(data.expenses);
  await noteModel.insertMany(data.notes);
}

export async function handleExportData() {
  const [expenses, notes] = await Promise.all([expenseModel.find(), noteModel.find()]);

  return { expenses, notes };
}

export function convertData(data: any): { expenses: ExpenseModel[]; notes: NoteModel[] } {
  if (data.spendingItem) {
    const expenses = data.spendingItem.map((item: any) => {
      const date = dayjs(item.atupdate).format('YYYY-MM-DD HH:mm:ss');
      return {
        name: item.nameitem,
        date: date,
        amount: item.price,
        type: ExpenseType.Expense,
        description: item.details == 'Không có thông tin' ? '' : item.details,
        recurring: false,
        createdAt: date,
        updatedAt: date,
      };
    });

    const notes = data.noted.map((item: any) => {
      const date = dayjs(item.atupdate).format('YYYY-MM-DD HH:mm:ss');
      return {
        title: item.namelist,
        content: item.content,
        createdAt: date,
        updatedAt: date,
      };
    });

    return { expenses, notes };
  } else return data;
}
