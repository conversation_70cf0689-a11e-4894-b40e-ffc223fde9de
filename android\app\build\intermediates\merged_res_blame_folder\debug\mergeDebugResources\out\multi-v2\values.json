{"logs": [{"outputFile": "com.alr.spendwise.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\697a983ff8b6be23efe7df3e3bbc5a94\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "378,449", "startColumns": "4,4", "startOffsets": "23812,29955", "endColumns": "67,166", "endOffsets": "23875,30117"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\ef234481c09f01fb9f0508a5da2b1126\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "40,41,43,44,45,46,284,285,286,287,288,289,290,377,765,766,767,1597,1599,1946,1955,1968", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1521,1581,1681,1750,1822,1885,18422,18496,18572,18648,18725,18796,18865,23744,51558,51639,51731,104168,104277,130136,130596,131371", "endLines": "40,41,43,44,45,46,284,285,286,287,288,289,290,377,765,766,767,1598,1600,1954,1967,1971", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "1576,1635,1745,1817,1880,1952,18491,18567,18643,18720,18791,18860,18931,23807,51634,51726,51819,104272,104393,130591,131366,131639"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\5a75dca28172537968edb11f4713fc67\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "371", "startColumns": "4", "startOffsets": "23387", "endColumns": "49", "endOffsets": "23432"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\d8c531c2afb462bf72eed5fcccc5521b\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "348,369", "startColumns": "4,4", "startOffsets": "22186,23273", "endColumns": "41,59", "endOffsets": "22223,23328"}}, {"source": "D:\\MyProjects\\SpendWise-New\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "4,11,18", "startColumns": "4,4,4", "startOffsets": "93,413,664", "endLines": "9,15,20", "endColumns": "12,12,12", "endOffsets": "407,657,810"}, "to": {"startLines": "490,496,501", "startColumns": "4,4,4", "startOffsets": "33806,34090,34339", "endLines": "495,500,503", "endColumns": "12,12,12", "endOffsets": "34085,34334,34485"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\824fcae5335998d0d778aa7eadecdfb2\\transformed\\androidbrowserhelper-2.5.0\\res\\values\\values.xml", "from": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "115,203,300,388", "endColumns": "87,96,87,91", "endOffsets": "198,295,383,475"}, "to": {"startLines": "476,477,478,484", "startColumns": "4,4,4,4", "startOffsets": "32678,32766,32863,33272", "endColumns": "87,96,87,91", "endOffsets": "32761,32858,32946,33359"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "39,56,57,75,76,127,128,252,253,254,255,256,257,258,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,344,345,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,379,411,412,413,414,415,416,417,482,1890,1891,1896,1899,1904,2079,2080,2782,2844,2990,3025,3055,3088", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1461,2575,2647,4000,4065,7565,7634,16119,16189,16257,16329,16399,16460,16534,17444,17505,17566,17628,17692,17754,17815,17883,17983,18043,18109,18182,18251,18308,18360,19601,19673,19749,19814,19873,19932,19992,20052,20112,20172,20232,20292,20352,20412,20472,20532,20591,20651,20711,20771,20831,20891,20951,21011,21071,21131,21191,21250,21310,21370,21429,21488,21547,21606,21665,22020,22055,22339,22394,22457,22512,22570,22628,22689,22752,22809,22860,22910,22971,23028,23094,23128,23163,23880,26112,26179,26251,26320,26389,26463,26535,33143,126137,126254,126521,126814,127081,140303,140375,164653,167150,174107,175913,176913,177595", "endLines": "39,56,57,75,76,127,128,252,253,254,255,256,257,258,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,344,345,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,379,411,412,413,414,415,416,417,482,1890,1894,1896,1902,1904,2079,2080,2787,2853,3024,3045,3087,3093", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1516,2642,2730,4060,4126,7629,7692,16184,16252,16324,16394,16455,16529,16602,17500,17561,17623,17687,17749,17810,17878,17978,18038,18104,18177,18246,18303,18355,18417,19668,19744,19809,19868,19927,19987,20047,20107,20167,20227,20287,20347,20407,20467,20527,20586,20646,20706,20766,20826,20886,20946,21006,21066,21126,21186,21245,21305,21365,21424,21483,21542,21601,21660,21719,22050,22085,22389,22452,22507,22565,22623,22684,22747,22804,22855,22905,22966,23023,23089,23123,23158,23193,23945,26174,26246,26315,26384,26458,26530,26618,33209,126249,126450,126626,127010,127205,140370,140437,164851,167446,175908,176589,177590,177757"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\538a1cd0f10e1c172df76f4159db47aa\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "69,70,71,72,231,232,460,463,464,465", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3636,3694,3760,3823,14641,14712,31324,31516,31583,31662", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3689,3755,3818,3880,14707,14779,31387,31578,31657,31726"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\7a4193c6fbbe5e128015b7f6283124c0\\transformed\\fragment-1.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "342,349,372,3046,3051", "startColumns": "4,4,4,4,4", "startOffsets": "21929,22228,23437,176594,176764", "endLines": "342,349,372,3050,3054", "endColumns": "56,64,63,24,24", "endOffsets": "21981,22288,23496,176759,176908"}}, {"source": "D:\\MyProjects\\SpendWise-New\\node_modules\\@capacitor\\android\\capacitor\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "100,175,251,331", "endColumns": "74,75,79,79", "endOffsets": "170,246,326,406"}, "to": {"startLines": "81,82,83,479", "startColumns": "4,4,4,4", "startOffsets": "4380,4455,4531,32951", "endColumns": "74,75,79,79", "endOffsets": "4450,4526,4606,33026"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\4489969c2b7608dadd0e6972380f5414\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "129,291,292,293,294,1895,1897,1898,1903,1905", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "7697,18936,18989,19042,19095,126455,126631,126753,127015,127210", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "7781,18984,19037,19090,19143,126516,126748,126809,127076,127272"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\0c35e3b0c2fe34519a603108fedf6f64\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "409", "startColumns": "4", "startOffsets": "25982", "endColumns": "82", "endOffsets": "26060"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\75881b531e34911967ea794bd3408c30\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "35,2081,2882,2888", "startColumns": "4,4,4,4", "startOffsets": "1251,140442,168808,169019", "endLines": "35,2083,2887,2971", "endColumns": "60,12,24,24", "endOffsets": "1307,140582,169014,173530"}}, {"source": "D:\\MyProjects\\SpendWise-New\\android\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "114", "startColumns": "4", "startOffsets": "6836", "endColumns": "56", "endOffsets": "6888"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\499bfd9d44ce23ed65691945ea1ce485\\transformed\\credentials-play-services-auth-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "1942", "startColumns": "4", "startOffsets": "129913", "endLines": "1945", "endColumns": "12", "endOffsets": "130131"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\ffb3076b6910ef8ba5c42525a0005009\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "34,77,78,79,80,233,234,235,525,1601,1603,1606,2788", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1199,4131,4192,4254,4316,14784,14843,14900,35941,104398,104462,104588,164856", "endLines": "34,77,78,79,80,233,234,235,531,1602,1605,1608,2815", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "1246,4187,4249,4311,4375,14838,14895,14949,36350,104457,104583,104711,165775"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\16884767c054ac4cab0f70a5a4855d4d\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2251,2267,2273,3390,3406", "startColumns": "4,4,4,4,4", "startOffsets": "147564,147989,148167,186960,187371", "endLines": "2266,2272,2282,3405,3409", "endColumns": "24,24,24,24,24", "endOffsets": "147984,148162,148446,187366,187493"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "36,37,38,42,47,48,49,50,51,52,53,54,55,58,59,60,61,63,64,65,66,67,68,73,74,104,105,106,107,108,109,110,111,112,113,115,116,117,118,119,120,121,122,123,124,125,126,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,259,260,262,263,264,265,266,267,268,295,296,297,298,299,300,301,302,338,339,340,341,343,346,347,350,367,373,374,375,376,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,481,485,486,487,488,489,504,512,513,517,521,532,537,543,550,554,558,563,567,571,575,579,583,587,593,597,603,607,613,617,622,626,629,633,639,643,649,653,659,662,666,670,674,678,682,683,684,685,688,691,694,697,701,702,703,704,705,708,710,712,714,719,720,724,730,734,735,737,749,750,754,760,764,768,769,773,800,804,805,809,837,1009,1035,1206,1232,1263,1271,1277,1293,1315,1320,1325,1335,1344,1353,1357,1364,1383,1390,1391,1400,1403,1406,1410,1414,1418,1421,1422,1427,1432,1442,1447,1454,1460,1461,1464,1468,1473,1475,1477,1480,1483,1485,1489,1492,1499,1502,1505,1509,1511,1515,1517,1519,1521,1525,1533,1541,1553,1559,1568,1571,1582,1585,1586,1591,1592,1619,1688,1758,1759,1769,1778,1779,1781,1785,1788,1791,1794,1797,1800,1803,1806,1810,1813,1816,1819,1823,1826,1830,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1856,1858,1859,1860,1861,1862,1863,1864,1865,1867,1868,1870,1871,1873,1875,1876,1878,1879,1880,1881,1882,1883,1885,1886,1887,1888,1889,1906,1908,1910,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1926,1927,1928,1929,1930,1931,1932,1934,1938,1972,1973,1974,1975,1976,1977,1981,1982,1983,1984,1986,1988,1990,1992,1994,1995,1996,1997,1999,2001,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2017,2018,2019,2020,2022,2024,2025,2027,2028,2030,2032,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2047,2048,2049,2050,2052,2053,2054,2055,2056,2058,2060,2062,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2137,2212,2215,2218,2221,2235,2241,2283,2286,2315,2342,2351,2415,2778,2816,2854,2972,3094,3118,3124,3143,3164,3288,3308,3314,3318,3324,3378,3410,3476,3496,3551,3563,3589", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1312,1367,1412,1640,1957,2012,2074,2138,2208,2269,2344,2420,2497,2735,2820,2902,2978,3110,3187,3265,3371,3477,3556,3885,3942,6155,6229,6304,6369,6435,6495,6556,6628,6701,6768,6893,6952,7011,7070,7129,7188,7242,7296,7349,7403,7457,7511,7786,7860,7939,8012,8086,8157,8229,8301,8374,8431,8489,8562,8636,8710,8785,8857,8930,9000,9071,9131,9192,9261,9330,9400,9474,9550,9614,9691,9767,9844,9909,9978,10055,10130,10199,10267,10344,10410,10471,10568,10633,10702,10801,10872,10931,10989,11046,11105,11169,11240,11312,11384,11456,11528,11595,11663,11731,11790,11853,11917,12007,12098,12158,12224,12291,12357,12427,12491,12544,12611,12672,12739,12852,12910,12973,13038,13103,13178,13251,13323,13367,13414,13460,13509,13570,13631,13692,13754,13818,13882,13946,14011,14074,14134,14195,14261,14320,14380,14442,14513,14573,16607,16693,16833,16923,17010,17098,17180,17263,17353,19148,19200,19258,19303,19369,19433,19490,19547,21724,21781,21829,21878,21986,22090,22137,22293,23198,23501,23565,23627,23687,23950,24024,24094,24172,24226,24296,24381,24429,24475,24536,24599,24665,24729,24800,24863,24928,24992,25053,25114,25166,25239,25313,25382,25457,25531,25605,25746,33090,33364,33442,33532,33620,33716,34490,35072,35161,35408,35689,36355,36640,37033,37510,37732,37954,38230,38457,38687,38917,39147,39377,39604,40023,40249,40674,40904,41332,41551,41834,42042,42173,42400,42826,43051,43478,43699,44124,44244,44520,44821,45145,45436,45750,45887,46018,46123,46365,46532,46736,46944,47215,47327,47439,47544,47661,47875,48021,48161,48247,48595,48683,48929,49347,49596,49678,49776,50433,50533,50785,51209,51464,51824,51913,52150,54174,54416,54518,54771,56927,67608,69124,79819,81347,83104,83730,84150,85411,86676,86932,87168,87715,88209,88814,89012,89592,90960,91335,91453,91991,92148,92344,92617,92873,93043,93184,93248,93613,93980,94656,94920,95258,95611,95705,95891,96197,96459,96584,96711,96950,97161,97280,97473,97650,98105,98286,98408,98667,98780,98967,99069,99176,99305,99580,100088,100584,101461,101755,102325,102474,103206,103378,103462,103798,103890,105251,110482,115853,115915,116493,117077,117168,117281,117510,117670,117822,117993,118159,118328,118495,118658,118901,119071,119244,119415,119689,119888,120093,120423,120507,120603,120699,120797,120897,120999,121101,121203,121305,121407,121507,121603,121715,121844,121967,122098,122229,122327,122441,122535,122675,122809,122905,123017,123117,123233,123329,123441,123541,123681,123817,123981,124111,124269,124419,124560,124704,124839,124951,125101,125229,125357,125493,125625,125755,125885,125997,127277,127423,127567,127705,127771,127861,127937,128041,128131,128233,128341,128449,128549,128629,128721,128819,128929,128981,129059,129165,129257,129361,129471,129593,129756,131644,131724,131824,131914,132024,132114,132355,132449,132555,132647,132747,132859,132973,133089,133205,133299,133413,133525,133627,133747,133869,133951,134055,134175,134301,134399,134493,134581,134693,134809,134931,135043,135218,135334,135420,135512,135624,135748,135815,135941,136009,136137,136281,136409,136478,136573,136688,136801,136900,137009,137120,137231,137332,137437,137537,137667,137758,137881,137975,138087,138173,138277,138373,138461,138579,138683,138787,138913,139001,139109,139209,139299,139409,139493,139595,139679,139733,139797,139903,139989,140099,140183,143524,146140,146258,146373,146453,146814,147047,148451,148529,149873,151234,151622,154465,164518,165780,167451,173535,177762,178513,178775,179290,179669,183947,184553,184782,184933,185148,186648,187498,190524,191268,193399,193739,195050", "endLines": "36,37,38,42,47,48,49,50,51,52,53,54,55,58,59,60,61,63,64,65,66,67,68,73,74,104,105,106,107,108,109,110,111,112,113,115,116,117,118,119,120,121,122,123,124,125,126,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,259,260,262,263,264,265,266,267,268,295,296,297,298,299,300,301,302,338,339,340,341,343,346,347,350,367,373,374,375,376,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,481,485,486,487,488,489,511,512,516,520,524,536,542,549,553,557,562,566,570,574,578,582,586,592,596,602,606,612,616,621,625,628,632,638,642,648,652,658,661,665,669,673,677,681,682,683,684,687,690,693,696,700,701,702,703,704,707,709,711,713,718,719,723,729,733,734,736,748,749,753,759,763,764,768,772,799,803,804,808,836,1008,1034,1205,1231,1262,1270,1276,1292,1314,1319,1324,1334,1343,1352,1356,1363,1382,1389,1390,1399,1402,1405,1409,1413,1417,1420,1421,1426,1431,1441,1446,1453,1459,1460,1463,1467,1472,1474,1476,1479,1482,1484,1488,1491,1498,1501,1504,1508,1510,1514,1516,1518,1520,1524,1532,1540,1552,1558,1567,1570,1581,1584,1585,1590,1591,1596,1687,1757,1758,1768,1777,1778,1780,1784,1787,1790,1793,1796,1799,1802,1805,1809,1812,1815,1818,1822,1825,1829,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1855,1857,1858,1859,1860,1861,1862,1863,1864,1866,1867,1869,1870,1872,1874,1875,1877,1878,1879,1880,1881,1882,1884,1885,1886,1887,1888,1889,1907,1909,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1925,1926,1927,1928,1929,1930,1931,1933,1937,1941,1972,1973,1974,1975,1976,1980,1981,1982,1983,1985,1987,1989,1991,1993,1994,1995,1996,1998,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2016,2017,2018,2019,2021,2023,2024,2026,2027,2029,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2046,2047,2048,2049,2051,2052,2053,2054,2055,2057,2059,2061,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2211,2214,2217,2220,2234,2240,2250,2285,2314,2341,2350,2414,2777,2781,2843,2881,2989,3117,3123,3129,3163,3287,3307,3313,3317,3323,3358,3389,3475,3495,3550,3562,3588,3595", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1362,1407,1456,1676,2007,2069,2133,2203,2264,2339,2415,2492,2570,2815,2897,2973,3049,3182,3260,3366,3472,3551,3631,3937,3995,6224,6299,6364,6430,6490,6551,6623,6696,6763,6831,6947,7006,7065,7124,7183,7237,7291,7344,7398,7452,7506,7560,7855,7934,8007,8081,8152,8224,8296,8369,8426,8484,8557,8631,8705,8780,8852,8925,8995,9066,9126,9187,9256,9325,9395,9469,9545,9609,9686,9762,9839,9904,9973,10050,10125,10194,10262,10339,10405,10466,10563,10628,10697,10796,10867,10926,10984,11041,11100,11164,11235,11307,11379,11451,11523,11590,11658,11726,11785,11848,11912,12002,12093,12153,12219,12286,12352,12422,12486,12539,12606,12667,12734,12847,12905,12968,13033,13098,13173,13246,13318,13362,13409,13455,13504,13565,13626,13687,13749,13813,13877,13941,14006,14069,14129,14190,14256,14315,14375,14437,14508,14568,14636,16688,16775,16918,17005,17093,17175,17258,17348,17439,19195,19253,19298,19364,19428,19485,19542,19596,21776,21824,21873,21924,22015,22132,22181,22334,23225,23560,23622,23682,23739,24019,24089,24167,24221,24291,24376,24424,24470,24531,24594,24660,24724,24795,24858,24923,24987,25048,25109,25161,25234,25308,25377,25452,25526,25600,25741,25811,33138,33437,33527,33615,33711,33801,35067,35156,35403,35684,35936,36635,37028,37505,37727,37949,38225,38452,38682,38912,39142,39372,39599,40018,40244,40669,40899,41327,41546,41829,42037,42168,42395,42821,43046,43473,43694,44119,44239,44515,44816,45140,45431,45745,45882,46013,46118,46360,46527,46731,46939,47210,47322,47434,47539,47656,47870,48016,48156,48242,48590,48678,48924,49342,49591,49673,49771,50428,50528,50780,51204,51459,51553,51908,52145,54169,54411,54513,54766,56922,67603,69119,79814,81342,83099,83725,84145,85406,86671,86927,87163,87710,88204,88809,89007,89587,90955,91330,91448,91986,92143,92339,92612,92868,93038,93179,93243,93608,93975,94651,94915,95253,95606,95700,95886,96192,96454,96579,96706,96945,97156,97275,97468,97645,98100,98281,98403,98662,98775,98962,99064,99171,99300,99575,100083,100579,101456,101750,102320,102469,103201,103373,103457,103793,103885,104163,110477,115848,115910,116488,117072,117163,117276,117505,117665,117817,117988,118154,118323,118490,118653,118896,119066,119239,119410,119684,119883,120088,120418,120502,120598,120694,120792,120892,120994,121096,121198,121300,121402,121502,121598,121710,121839,121962,122093,122224,122322,122436,122530,122670,122804,122900,123012,123112,123228,123324,123436,123536,123676,123812,123976,124106,124264,124414,124555,124699,124834,124946,125096,125224,125352,125488,125620,125750,125880,125992,126132,127418,127562,127700,127766,127856,127932,128036,128126,128228,128336,128444,128544,128624,128716,128814,128924,128976,129054,129160,129252,129356,129466,129588,129751,129908,131719,131819,131909,132019,132109,132350,132444,132550,132642,132742,132854,132968,133084,133200,133294,133408,133520,133622,133742,133864,133946,134050,134170,134296,134394,134488,134576,134688,134804,134926,135038,135213,135329,135415,135507,135619,135743,135810,135936,136004,136132,136276,136404,136473,136568,136683,136796,136895,137004,137115,137226,137327,137432,137532,137662,137753,137876,137970,138082,138168,138272,138368,138456,138574,138678,138782,138908,138996,139104,139204,139294,139404,139488,139590,139674,139728,139792,139898,139984,140094,140178,140298,146135,146253,146368,146448,146809,147042,147559,148524,149868,151229,151617,154460,164513,164648,167145,168803,174102,178508,178770,178970,179664,183942,184548,184777,184928,185143,186226,186955,190519,191263,193394,193734,195045,195248"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\0dd0d3e8de446dcc2045bd19e68aeec6\\transformed\\facebook-common-18.0.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,49,54,60,71,82,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,202,290,370,449,513,590,665,732,814,895,968,1023,1092,1171,1235,1309,1382,1455,1527,1600,1673,1738,1807,4164,4458,4808,5429,6060,6239", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,53,59,70,81,84,112", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "197,285,365,444,508,585,660,727,809,890,963,1018,1087,1166,1230,1304,1377,1450,1522,1595,1668,1733,1802,1867,4453,4803,5424,6055,6234,7468"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,236,237,238,240,241,242,243,244,245,246,247,248,2092,2097,2103,2114,2125,3596", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4611,4663,4751,4831,4910,4974,5051,5126,5193,5275,5356,5429,14954,15023,15102,15236,15310,15383,15456,15528,15601,15674,15739,15808,140985,141279,141629,142250,142881,195253", "endLines": "84,85,86,87,88,89,90,91,92,93,94,95,236,237,238,240,241,242,243,244,245,246,247,248,2096,2102,2113,2124,2127,3623", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "4658,4746,4826,4905,4969,5046,5121,5188,5270,5351,5424,5479,15018,15097,15161,15305,15378,15451,15523,15596,15669,15734,15803,15868,141274,141624,142245,142876,143055,196482"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\998c918bf96ae2f6a4f5c8c644413a6f\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "370", "startColumns": "4", "startOffsets": "23333", "endColumns": "53", "endOffsets": "23382"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\312f1400fd07d3c14522fb116debe31d\\transformed\\credentials-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "407,408", "startColumns": "4,4", "startOffsets": "25816,25898", "endColumns": "81,83", "endOffsets": "25893,25977"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\1e2a4bdd6b7dd0849fedecef3b361d31\\transformed\\facebook-login-18.0.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,38,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,175,257,340,421,558,673,791,886,951,1019,1077,1149,1221,1318,1409,1483,1557,1670,1771,1834,2054,2219,2296,2378,2503,2589,2713,2799,3177,3880", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,37,50,59", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "170,252,335,416,553,668,786,881,946,1014,1072,1144,1216,1313,1404,1478,1552,1665,1766,1829,2049,2214,2291,2373,2498,2584,2708,2794,3172,3875,4296"}, "to": {"startLines": "239,249,250,251,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,2128,2129,3624,3637", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15166,15873,15955,16038,26623,26760,26875,26993,27088,27153,27221,27279,27351,27423,27520,27611,27685,27759,27872,27973,28036,28256,28421,28498,28580,28705,28791,143060,143146,196487,197190", "endLines": "239,249,250,251,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,2128,2136,3636,3645", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "15231,15950,16033,16114,26755,26870,26988,27083,27148,27216,27274,27346,27418,27515,27606,27680,27754,27867,27968,28031,28251,28416,28493,28575,28700,28786,28910,143141,143519,197185,197606"}}, {"source": "D:\\MyProjects\\SpendWise-New\\node_modules\\@capacitor\\splash-screen\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,4,7", "startColumns": "4,4,4", "startOffsets": "55,141,289", "endLines": "3,6,9", "endColumns": "12,12,12", "endOffsets": "136,284,448"}, "to": {"startLines": "2084,2086,2089", "startColumns": "4,4,4", "startOffsets": "140587,140673,140821", "endLines": "2085,2088,2091", "endColumns": "12,12,12", "endOffsets": "140668,140816,140980"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\f212eb2fcec7b76a8049b85cea08416b\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "368", "startColumns": "4", "startOffsets": "23230", "endColumns": "42", "endOffsets": "23268"}}, {"source": "D:\\MyProjects\\SpendWise-New\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,219,160,102", "endColumns": "46,63,58,57", "endOffsets": "97,278,214,155"}, "to": {"startLines": "410,461,480,483", "startColumns": "4,4,4,4", "startOffsets": "26065,31392,33031,33214", "endColumns": "46,63,58,57", "endOffsets": "26107,31451,33085,33267"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\eb1abcd433ebd6830d96edea1f30a050\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,62,261,459,462,466,467,468,469,470,471,472,473,474,475", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,3054,16780,31248,31456,31731,31820,31919,32027,32124,32212,32312,32382,32479,32589", "endLines": "5,7,10,14,33,62,261,459,462,466,467,468,469,470,471,472,473,474,475", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,3105,16828,31319,31511,31815,31914,32022,32119,32207,32307,32377,32474,32584,32673"}}, {"source": "D:\\MyProjects\\SpendWise-New\\node_modules\\@capgo\\capacitor-social-login\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,9", "startColumns": "4,4", "startOffsets": "55,435", "endLines": "8,11", "endColumns": "8,12", "endOffsets": "430,585"}, "to": {"startLines": "1609,1616", "startColumns": "4,4", "startOffsets": "104716,105096", "endLines": "1615,1618", "endColumns": "8,12", "endOffsets": "105091,105246"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\3897ee7a3a7e64eb47ff9b7bb8256b24\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "96,97,98,99,100,101,102,103,441,442,443,444,445,446,447,448,450,451,452,453,454,455,456,457,458,3130,3359", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5484,5574,5654,5744,5834,5914,5995,6075,28915,29020,29201,29326,29433,29613,29736,29852,30122,30310,30415,30596,30721,30896,31044,31107,31169,178975,186231", "endLines": "96,97,98,99,100,101,102,103,441,442,443,444,445,446,447,448,450,451,452,453,454,455,456,457,458,3142,3377", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5569,5649,5739,5829,5909,5990,6070,6150,29015,29196,29321,29428,29608,29731,29847,29950,30305,30410,30591,30716,30891,31039,31102,31164,31243,179285,186643"}}]}]}