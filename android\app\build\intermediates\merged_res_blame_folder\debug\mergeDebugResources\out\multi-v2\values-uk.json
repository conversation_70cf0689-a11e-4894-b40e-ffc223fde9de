{"logs": [{"outputFile": "com.alr.spendwise.app-mergeDebugResources-2:/values-uk/values-uk.xml", "map": [{"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\312f1400fd07d3c14522fb116debe31d\\transformed\\credentials-1.3.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2834,2944", "endColumns": "109,118", "endOffsets": "2939,3058"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\3897ee7a3a7e64eb47ff9b7bb8256b24\\transformed\\play-services-base-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5126,5234,5397,5524,5634,5788,5917,6032,6283,6451,6557,6719,6844,6991,7133,7203,7264", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "5229,5392,5519,5629,5783,5912,6027,6132,6446,6552,6714,6839,6986,7128,7198,7259,7347"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\538a1cd0f10e1c172df76f4159db47aa\\transformed\\browser-1.4.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "71,73,74,75", "startColumns": "4,4,4,4", "startOffsets": "7459,7662,7769,7889", "endColumns": "109,106,119,107", "endOffsets": "7564,7764,7884,7992"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\1e2a4bdd6b7dd0849fedecef3b361d31\\transformed\\facebook-login-18.0.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,235,315,407,496,609,729,818,907,1007,1092,1177,1277", "endColumns": "87,91,79,91,88,112,119,88,88,99,84,84,99,113", "endOffsets": "138,230,310,402,491,604,724,813,902,1002,1087,1172,1272,1386"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3790,3878,3970,4050,4142,4231,4344,4464,4553,4642,4742,4827,4912,5012", "endColumns": "87,91,79,91,88,112,119,88,88,99,84,84,99,113", "endOffsets": "3873,3965,4045,4137,4226,4339,4459,4548,4637,4737,4822,4907,5007,5121"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\eb1abcd433ebd6830d96edea1f30a050\\transformed\\biometric-1.1.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,255,382,513,654,765,892,1026,1125,1255,1387", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "157,250,377,508,649,760,887,1021,1120,1250,1382,1507"}, "to": {"startLines": "70,72,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7352,7569,7997,8124,8255,8396,8507,8634,8768,8867,8997,9129", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "7454,7657,8119,8250,8391,8502,8629,8763,8862,8992,9124,9249"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\dda665aa4a1576cfb1759fb2bbcd5279\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,9254", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,9331"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\697a983ff8b6be23efe7df3e3bbc5a94\\transformed\\play-services-basement-18.4.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "6137", "endColumns": "145", "endOffsets": "6278"}}, {"source": "D:\\Tools\\.gradle\\caches\\8.11.1\\transforms\\5f51ed623ec66baebfa6a053fe8a8b2a\\transformed\\core-1.15.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "31,32,33,34,35,36,37,87", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3063,3163,3265,3366,3467,3572,3677,9336", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3158,3260,3361,3462,3567,3672,3785,9432"}}]}]}